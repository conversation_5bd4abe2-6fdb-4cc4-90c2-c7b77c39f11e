//
//  ContentView.swift
//  testLearn
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/1.
//

import SwiftUI
import WebKit

struct WebView: UIViewRepresentable {
    
    let urlString: String
    let cookies: [HTTPCookie]
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView(frame: .zero, configuration: configureWKWebView())
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        guard let url = URL(string: urlString) else {
            return
        }
        
        // 使用DispatchGroup来等待所有cookies设置完成
        let group = DispatchGroup()
        
        // 注入cookies到WebView
        for cookie in cookies {
            group.enter()
            webView.configuration.websiteDataStore.httpCookieStore.setCookie(cookie) {
                group.leave()
            }
        }
        
        // 等待所有cookies设置完成后再加载页面
        group.notify(queue: .main) {
            print("所有cookies已设置完成，开始加载页面")
            webView.load(URLRequest(url: url))
        }
    }
    
    private func configureWKWebView() -> WKWebViewConfiguration {
        let config = WKWebViewConfiguration()
        config.websiteDataStore = WKWebsiteDataStore.default()
        return config
    }
}

struct ContentView: View {
    var body: some View {
        WebView(urlString: "http://*************:5173", cookies: createCookies())
    }
    
    private func createCookies() -> [HTTPCookie] {
        guard let url = URL(string: "http://*************:5173/") else {
            return []
        }
        
        let cookie1 = HTTPCookie(properties: [
            .domain: url.host!,
            .path: "/",
            .name: "ExampleCookieName1",
            .value: "ExampleValue1",
            .secure: false,  // 修复：使用布尔值而不是字符串
            .expires: NSDate(timeIntervalSinceNow: 31556926)
        ])!
        
        let cookie2 = HTTPCookie(properties: [
            .domain: url.host!,
            .path: "/",
            .name: "ExampleCookieName2",
            .value: "ExampleValue2",
            .secure: false,  // 修复：使用布尔值而不是字符串
            .expires: NSDate(timeIntervalSinceNow: 31556926)
        ])!
        
        return [cookie1, cookie2]
    }
}

#Preview {
    ContentView()
}
