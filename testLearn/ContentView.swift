//
//  ContentView.swift
//  testLearn
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/1.
//

import SwiftUI
import WebKit
import OSLog

// MARK: - WebView ViewModel
@Observable
class WebViewModel {
    var isLoading = false
    var cookiesInjected = false
    private let logger = Logger(subsystem: "com.jack.testLearn", category: "WebView")

    let urlString = "http://*************:5173"

    func createCookies() -> [HTTPCookie] {
        guard let url = URL(string: urlString) else {
            logger.error("无法创建URL: \(self.urlString)")
            return []
        }

        let cookie1Properties: [HTTPCookiePropertyKey: Any] = [
            .domain: url.host!,
            .path: "/",
            .name: "ExampleCookieName1",
            .value: "ExampleValue1",
            .secure: false,
            .expires: NSDate(timeIntervalSinceNow: 31556926)
        ]

        let cookie2Properties: [HTTPCookiePropertyKey: Any] = [
            .domain: url.host!,
            .path: "/",
            .name: "ExampleCookieName2",
            .value: "ExampleValue2",
            .secure: false,
            .expires: NSDate(timeIntervalSinceNow: 31556926)
        ]

        guard let cookie1 = HTTPCookie(properties: cookie1Properties),
              let cookie2 = HTTPCookie(properties: cookie2Properties) else {
            logger.error("创建Cookie失败")
            return []
        }

        logger.info("成功创建 \(2) 个Cookie")
        return [cookie1, cookie2]
    }

    func logCookieDetails(_ cookie: HTTPCookie) {
        logger.info("Cookie详情 - 名称: \(cookie.name), 值: \(cookie.value), 域名: \(cookie.domain), 路径: \(cookie.path), httpOnly: \(cookie.isHTTPOnly), secure: \(cookie.isSecure)")
    }
}

// MARK: - WebView UIViewRepresentable
struct WebView: UIViewRepresentable {
    @Bindable var viewModel: WebViewModel
    private let logger = Logger(subsystem: "com.jack.testLearn", category: "WebView")

    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView(frame: .zero, configuration: configureWKWebView())
        webView.navigationDelegate = context.coordinator
        return webView
    }

    func updateUIView(_ webView: WKWebView, context: Context) {
        guard !viewModel.cookiesInjected else {
            logger.info("Cookie已注入，跳过重复注入")
            return
        }

        injectCookiesAndLoadPage(webView: webView)
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    private func configureWKWebView() -> WKWebViewConfiguration {
        let config = WKWebViewConfiguration()
        config.websiteDataStore = WKWebsiteDataStore.default()

        // 允许任意加载（用于开发环境）
        config.preferences.setValue(true, forKey: "allowFileAccessFromFileURLs")

        return config
    }

    private func injectCookiesAndLoadPage(webView: WKWebView) {
        guard let url = URL(string: viewModel.urlString) else {
            logger.error("无法创建URL: \(self.viewModel.urlString)")
            return
        }

        viewModel.isLoading = true
        let cookies = viewModel.createCookies()

        logger.info("开始注入 \(cookies.count) 个Cookie")

        // 使用Task来处理异步Cookie注入
        Task { @MainActor in
            do {
                // 逐个注入Cookie并等待完成
                for cookie in cookies {
                    viewModel.logCookieDetails(cookie)

                    await withCheckedContinuation { continuation in
                        webView.configuration.websiteDataStore.httpCookieStore.setCookie(cookie) {
                            self.logger.info("Cookie注入成功: \(cookie.name)")
                            continuation.resume()
                        }
                    }
                }

                // 所有Cookie注入完成后，等待一小段时间确保生效
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

                logger.info("所有Cookie注入完成，开始加载页面")
                viewModel.cookiesInjected = true

                // 加载页面
                webView.load(URLRequest(url: url))

            } catch {
                logger.error("Cookie注入过程中发生错误: \(error.localizedDescription)")
                viewModel.isLoading = false
            }
        }
    }

    // MARK: - Coordinator
    class Coordinator: NSObject, WKNavigationDelegate {
        var parent: WebView
        private let logger = Logger(subsystem: "com.jack.testLearn", category: "WebView.Coordinator")

        init(_ parent: WebView) {
            self.parent = parent
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            logger.info("开始加载页面")
            parent.viewModel.isLoading = true
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            logger.info("页面加载完成")
            parent.viewModel.isLoading = false

            // 页面加载完成后，验证Cookie是否可以被JavaScript读取
            let script = """
                const cookies = document.cookie;
                console.log('当前页面Cookie:', cookies);
                cookies;
            """

            webView.evaluateJavaScript(script) { result, error in
                if let error = error {
                    self.logger.error("JavaScript执行错误: \(error.localizedDescription)")
                } else if let cookieString = result as? String {
                    self.logger.info("JavaScript读取到的Cookie: \(cookieString)")
                } else {
                    self.logger.warning("JavaScript未读取到Cookie")
                }
            }
        }

        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            logger.error("页面加载失败: \(error.localizedDescription)")
            parent.viewModel.isLoading = false
        }
    }
}

// MARK: - ContentView
struct ContentView: View {
    @State private var viewModel = WebViewModel()

    var body: some View {
        NavigationStack {
            ZStack {
                WebView(viewModel: viewModel)

                if viewModel.isLoading {
                    ProgressView("加载中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                }
            }
            .navigationTitle("Cookie测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ContentView()
}
